<template>
  <div class="task-flow-gantt">
    <div class="gantt-header">
      <h3 class="gantt-title">任务流执行时序图</h3>
      <div class="gantt-controls">
        <div class="control-tip">
          <i class="el-icon-info"></i>
          <span>鼠标滚轮缩放 | 拖拽移动</span>
        </div>
        <el-button size="small" @click="resetZoom">重置缩放</el-button>
      </div>
    </div>

    <div class="gantt-legend">
      <div class="legend-item" v-for="eventType in eventTypes" :key="eventType.name">
        <span class="legend-color" :style="{ backgroundColor: eventType.color }"></span>
        <span class="legend-text">{{ eventType.label }}</span>
      </div>
    </div>

    <div
      ref="ganttChart"
      class="gantt-chart-container"
      v-loading="loading"
    >
      <div v-if="!loading && (!rawEvents || rawEvents.length === 0)" class="empty-state">
        <i class="el-icon-data-line"></i>
        <p>暂无时序数据</p>
        <el-button size="small" type="primary" @click="refreshData">重新加载</el-button>
      </div>
    </div>

    <div class="gantt-summary" v-if="summaryData">
      <div class="summary-item">
        <span class="summary-label">总事件数:</span>
        <span class="summary-value">{{ summaryData.total_events }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">任务数:</span>
        <span class="summary-value">{{ summaryData.total_jobs }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">计算中心数:</span>
        <span class="summary-value">{{ summaryData.total_centers }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">总时长:</span>
        <span class="summary-value">{{ totalDuration }}s</span>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'TaskFlowGantt',
  props: {
    // 任务ID，用于从API获取时序数据
    taskId: {
      type: [String, Number],
      required: true
    },
    // 可以通过props传入数据，如果没有则从API加载
    eventsData: {
      type: Array,
      default: null
    },
    height: {
      type: String,
      default: '500px'
    }
  },
  data() {
    return {
      loading: false,
      ganttChart: null,
      rawEvents: [],
      summaryData: null,
      totalDuration: 0,

      // 事件类型配色方案 - 深色主题优化
      eventTypes: [
        { name: 'submit', label: '提交', color: '#00D4AA' },
        { name: 'schedule', label: '调度', color: '#FFB800' },
        { name: 'start', label: '开始执行', color: '#32C5FF' },
        { name: 'complete', label: '完成执行', color: '#8B5CF6' },
        { name: 'transfer', label: '数据传输', color: '#FF6B6B' },
        { name: 'transmission_complete', label: '传输完成', color: '#4ECDC4' }
      ]
    }
  },
  computed: {
    // 获取事件类型颜色映射
    eventColorMap() {
      const map = {}
      this.eventTypes.forEach(type => {
        map[type.name] = type.color
      })
      return map
    }
  },
  watch: {
    // 监听taskId变化，重新加载数据
    taskId(newVal, oldVal) {
      if (newVal && newVal !== oldVal) {
        this.loadData()
      }
    },
    // 监听eventsData变化
    eventsData(newVal) {
      if (newVal) {
        this.processData({ events: newVal })
      }
    }
  },
  mounted() {
    // 延迟初始化图表，确保DOM已经渲染完成
    this.$nextTick(() => {
      this.initChart()
      this.loadData()
    })

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.ganttChart) {
      this.ganttChart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 初始化图表
    initChart() {
      this.ganttChart = this.$echarts.init(this.$refs.ganttChart)
    },

    // 加载数据
    async loadData() {
      if (this.eventsData) {
        // 使用传入的数据
        this.processData({ events: this.eventsData })
        return
      }

      if (!this.taskId) {
        console.warn('缺少taskId参数')
        this.$message.warning('缺少任务ID参数')
        return
      }

      // 从API接口加载数据
      this.loading = true
      try {
        // 直接使用axios调用API
        const apiUrl = `/api/v1/yaml/timeline/${this.taskId}`
        const response = await axios.get(apiUrl)

        // 解析响应数据
        let timelineData = null
        if (response && response.data) {
          if (response.data.success && response.data.data) {
            timelineData = response.data.data
          } else if (response.data.payload) {
            timelineData = response.data.payload
          } else {
            timelineData = response.data
          }
        }

        if (timelineData) {
          this.processData(timelineData)
        } else {
          console.warn('API返回数据格式错误:', response.data)
          this.$message.warning('获取时序数据失败：数据格式错误')
        }
      } catch (error) {
        console.error('加载时序数据失败:', error)
        if (error.response) {
          // 服务器返回错误状态码
          const status = error.response.status
          const message = error.response.data?.message || '服务器错误'
          this.$message.error(`加载时序数据失败 (${status}): ${message}`)
        } else if (error.request) {
          // 网络错误
          this.$message.error('网络连接失败，请检查网络连接')
        } else {
          // 其他错误
          this.$message.error('加载时序数据失败：' + error.message)
        }
      } finally {
        this.loading = false
      }
    },

    // 处理数据并渲染图表
    processData(data) {
      // 检查数据是否为null或undefined
      if (!data) {
        this.$message.warning('暂无时序数据')
        return
      }

      // 解析数据结构
      let events = null
      let summary = null
      let totalDuration = 0

      if (Array.isArray(data)) {
        // 如果data直接是events数组
        events = data
      } else if (data.events && Array.isArray(data.events)) {
        // 如果data是包含events的对象
        events = data.events
        summary = data.summary
        totalDuration = data.total_duration || 0
      } else {
        console.warn('无效的时序数据格式，期望包含events数组:', data)
        this.$message.warning('时序数据格式不正确')
        return
      }

      this.rawEvents = events
      this.summaryData = summary
      this.totalDuration = totalDuration

      // 转换数据为甘特图格式
      const ganttData = this.convertToGanttData(this.rawEvents)

      if (ganttData.data.length === 0) {
        this.$message.info('暂无时序数据可显示')
        return
      }

      this.renderChart(ganttData)
    },

    // 将事件数据转换为甘特图数据
    convertToGanttData(events) {
      // 按job_id分组并构建时间段
      const jobTimelines = {}
      const jobs = [...new Set(events.map(e => e.job_id))]

      jobs.forEach(jobId => {
        jobTimelines[jobId] = []
        const jobEvents = events.filter(e => e.job_id === jobId).sort((a, b) => a.timestamp - b.timestamp)

        let currentTask = null

        jobEvents.forEach(event => {
          if (event.event_type === 'start') {
            // 开始一个新的执行任务
            currentTask = {
              jobId: jobId,
              startTime: event.timestamp,
              endTime: null,
              eventType: 'execution',
              centerId: event.center_id,
              epoch: event.epoch
            }
          } else if (event.event_type === 'complete' && currentTask) {
            // 完成当前执行任务
            currentTask.endTime = event.timestamp
            jobTimelines[jobId].push(currentTask)
            currentTask = null
          } else if (event.event_type === 'transfer') {
            // 数据传输事件
            jobTimelines[jobId].push({
              jobId: jobId,
              startTime: event.timestamp,
              endTime: event.timestamp + 2, // 假设传输持续2个时间单位
              eventType: 'transfer',
              sourceCenterId: event.source_center_id,
              targetCenterId: event.target_center_id
            })
          }
        })
      })

      // 转换为ECharts甘特图数据格式
      const ganttData = []
      const categories = []

      jobs.forEach((jobId, index) => {
        categories.push(`Job ${jobId}`)

        jobTimelines[jobId].forEach(task => {
          if (task.endTime) {
            ganttData.push({
              name: task.eventType === 'execution'
                ? `执行 Epoch ${task.epoch} (Center ${task.centerId})`
                : `传输 (${task.sourceCenterId}→${task.targetCenterId})`,
              value: [
                index, // Y轴位置
                task.startTime, // 开始时间
                task.endTime, // 结束时间
                task.endTime - task.startTime // 持续时间
              ],
              itemStyle: {
                color: task.eventType === 'execution' ? this.eventColorMap.start : this.eventColorMap.transfer
              }
            })
          }
        })
      })

      return { data: ganttData, categories }
    },

    // 渲染图表
    renderChart(ganttData) {
      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const data = params.data
            const duration = data.value[3]
            const startTime = data.value[1]
            const endTime = data.value[2]
            return `
              <div style="
                background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                border: 1px solid #32c5ff;
                border-radius: 8px;
                padding: 12px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                color: #fff;
                font-size: 13px;
                line-height: 1.5;
              ">
                <div style="font-weight: bold; color: #32c5ff; margin-bottom: 8px;">${data.name}</div>
                <div style="margin-bottom: 4px;">⏰ 开始时间: <span style="color: #00D4AA;">${startTime}s</span></div>
                <div style="margin-bottom: 4px;">⏱️ 结束时间: <span style="color: #FFB800;">${endTime}s</span></div>
                <div>⏳ 持续时间: <span style="color: #FF6B6B;">${duration}s</span></div>
              </div>
            `
          },
          backgroundColor: 'transparent',
          borderWidth: 0,
          extraCssText: 'box-shadow: none;'
        },
        grid: {
          left: 100,
          right: 30,
          top: 40,
          bottom: 60,
          containLabel: true
        },
        xAxis: {
          type: 'value',
          name: '时间轴 (秒)',
          nameLocation: 'middle',
          nameGap: 35,
          nameTextStyle: {
            color: '#32c5ff',
            fontSize: 14,
            fontWeight: 'bold'
          },
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.9)',
            fontSize: 12,
            formatter: '{value}s',
            margin: 8
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#32c5ff',
              width: 2
            }
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: '#32c5ff'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(50, 197, 255, 0.1)',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: ganttData.categories,
          axisLabel: {
            color: 'rgba(255, 255, 255, 0.9)',
            fontSize: 13,
            fontWeight: 'bold',
            margin: 10
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#32c5ff',
              width: 2
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: [{
          type: 'custom',
          renderItem: this.renderGanttItem,
          encode: {
            x: [1, 2],
            y: 0
          },
          data: ganttData.data
        }],
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: 0,
            zoomOnMouseWheel: true,
            moveOnMouseMove: true,
            preventDefaultMouseMove: false
          }
        ]
      }

      this.ganttChart.setOption(option, true)
    },

    // 自定义渲染甘特图项目
    renderGanttItem(params, api) {
      const categoryIndex = api.value(0)
      const start = api.coord([api.value(1), categoryIndex])
      const end = api.coord([api.value(2), categoryIndex])
      const height = api.size([0, 1])[1] * 0.7

      const rectShape = this.$echarts.graphic.clipRectByRect({
        x: start[0],
        y: start[1] - height / 2,
        width: end[0] - start[0],
        height: height
      }, {
        x: params.coordSys.x,
        y: params.coordSys.y,
        width: params.coordSys.width,
        height: params.coordSys.height
      })

      if (!rectShape) return null

      const itemStyle = api.style()
      const baseColor = itemStyle.fill

      return {
        type: 'rect',
        transition: ['shape'],
        shape: rectShape,
        style: {
          fill: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: baseColor },
            { offset: 1, color: this.adjustColorBrightness(baseColor, -20) }
          ]),
          stroke: this.adjustColorBrightness(baseColor, 30),
          strokeWidth: 2,
          shadowColor: baseColor,
          shadowBlur: 8,
          shadowOffsetX: 2,
          shadowOffsetY: 2
        }
      }
    },

    // 调整颜色亮度
    adjustColorBrightness(color, amount) {
      const usePound = color[0] === '#'
      const col = usePound ? color.slice(1) : color
      const num = parseInt(col, 16)
      let r = (num >> 16) + amount
      let g = (num >> 8 & 0x00FF) + amount
      let b = (num & 0x0000FF) + amount
      r = r > 255 ? 255 : r < 0 ? 0 : r
      g = g > 255 ? 255 : g < 0 ? 0 : g
      b = b > 255 ? 255 : b < 0 ? 0 : b
      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0')
    },

    // 刷新数据
    refreshData() {
      this.loadData()
    },

    // 重置缩放
    resetZoom() {
      if (this.ganttChart) {
        this.ganttChart.dispatchAction({
          type: 'dataZoom',
          start: 0,
          end: 100
        })
      }
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.ganttChart) {
        this.ganttChart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.task-flow-gantt {
  background: linear-gradient(135deg, rgba(30, 60, 114, 0.1) 0%, rgba(42, 82, 152, 0.1) 100%);
  border-radius: 12px;
  padding: 20px;

  .gantt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(50, 197, 255, 0.3);
    background: linear-gradient(90deg, rgba(50, 197, 255, 0.1) 0%, transparent 100%);
    border-radius: 8px;
    padding: 15px 20px;

    .gantt-title {
      margin: 0;
      color: #32c5ff;
      font-size: 18px;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .gantt-controls {
      display: flex;
      align-items: center;
      gap: 16px;

      .control-tip {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 6px 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.1);

        i {
          color: #32c5ff;
          font-size: 14px;
        }

        span {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
        }
      }

      .el-button {
        border-radius: 20px;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;

        &.el-button--primary {
          background: linear-gradient(135deg, #32c5ff 0%, #1e90ff 100%);
          border: none;
          box-shadow: 0 4px 12px rgba(50, 197, 255, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(50, 197, 255, 0.4);
          }
        }

        &.el-button--default {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.9);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #32c5ff;
            color: #32c5ff;
          }
        }
      }
    }
  }

  .gantt-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
    border-radius: 12px;
    border: 1px solid rgba(50, 197, 255, 0.2);
    backdrop-filter: blur(10px);

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 20px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-1px);
      }

      .legend-color {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.2);
      }

      .legend-text {
        font-size: 13px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }
    }
  }

  .gantt-chart-container {
    height: v-bind(height);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.05) 100%);
    border: 2px solid rgba(50, 197, 255, 0.3);
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    .empty-state {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: rgba(255, 255, 255, 0.7);

      i {
        font-size: 64px;
        margin-bottom: 20px;
        display: block;
        color: rgba(50, 197, 255, 0.6);
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      p {
        margin: 0 0 20px 0;
        font-size: 16px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
      }

      .el-button {
        border-radius: 20px;
        padding: 10px 20px;
        font-weight: 500;
        background: linear-gradient(135deg, #32c5ff 0%, #1e90ff 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(50, 197, 255, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(50, 197, 255, 0.4);
        }
      }
    }
  }

  .gantt-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
    border-radius: 16px;
    border: 1px solid rgba(50, 197, 255, 0.2);
    backdrop-filter: blur(10px);

    .summary-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 12px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-color: rgba(50, 197, 255, 0.3);
        box-shadow: 0 4px 16px rgba(50, 197, 255, 0.2);
      }

      .summary-label {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 8px;
        font-weight: 500;
        text-align: center;
      }

      .summary-value {
        font-size: 20px;
        font-weight: 700;
        color: #32c5ff;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        background: linear-gradient(135deg, #32c5ff 0%, #00d4aa 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }
}
</style>
